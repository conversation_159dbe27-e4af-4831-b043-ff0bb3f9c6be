<template>
  <div class="dashboard-container">
    <div class="main-layout">
      <!-- 工作概览和个人信息 -->
      <el-row :gutter="20">
        <el-col :xs="24" :sm="6" :md="4">
          <div class="stat-card">
            <div class="icon-container blue">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="number">3</div>
              <div class="text">{{ $t('page.dashboard.managedUsers') }}</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="6" :md="4">
          <div class="stat-card">
            <div class="icon-container red">
              <el-icon><Bell /></el-icon>
            </div>
            <div class="stat-content">
              <div class="number">3</div>
              <div class="text">{{ $t('page.dashboard.todoItems') }}</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="6" :md="4">
          <div class="stat-card">
            <div class="icon-container green">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="number">3</div>
              <div class="text">{{ $t('page.dashboard.warningItems') }}</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="6" :md="4">
          <div class="stat-card">
            <div class="icon-container purple">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="number">18</div>
              <div class="text">{{ $t('page.dashboard.documentCount') }}</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :md="8">
          <div class="personal-info-card">
            <div v-if="isLoadingProfile" class="profile-section loading-state">
              <el-skeleton animated style="width: 100%">
                <template #template>
                  <div style="display: flex; align-items: center; height: 80px;">
                    <el-skeleton-item variant="circle" style="width: 70px; height: 70px; margin-right: 15px; flex-shrink: 0;" />
                    <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                      <el-skeleton-item variant="h3" style="width: 50%; margin-bottom: 8px;" />
                      <el-skeleton-item variant="text" style="width: 60%; margin-bottom: 4px;" />
                      <el-skeleton-item variant="text" style="width: 80%; margin-bottom: 4px;" />
                      <el-skeleton-item variant="text" style="width: 70%;" />
                    </div>
                  </div>
                </template>
              </el-skeleton>
            </div>
            <div v-else class="profile-section">
              <img :src="userProfile?.avatar || '/default-avatar.png'" class="avatar" />
              <div class="info">
                <div class="name">{{ userProfile?.real_name || $t('page.profile.realName') }}</div>
                <div class="role-item">
                  <div class="icon-text">
                    <el-icon><Avatar /></el-icon>
                    <span>{{ userProfile?.role || $t('page.profile.role') }}</span>
                  </div>
                </div>
                <div class="role-item">
                  <div class="icon-text">
                    <el-icon><Location /></el-icon>
                    <span>{{ $t('page.profile.department') }}：{{ userProfile?.department_name || '未设置' }}</span>
                  </div>
                </div>
                <div class="role-item">
                  <div class="icon-text">
                    <el-icon><School /></el-icon>
                    <span>{{ $t('page.dashboard.workOverview') }}：{{ userProfile?.department_name || '未设置' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 待办事项、我发起和统计图表在同一行 -->
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="8">
          <div class="list-container">
            <div class="list-header">
              <div class="tab-group">
                <div
                  :class="['tab', {'active': activeTodoTab === 'pending'}]"
                  @click="switchTodoTab('pending')"
                >{{ $t('page.dashboard.todoItems') }}</div>
                <div
                  :class="['tab', {'active': activeTodoTab === 'completed'}]"
                  @click="switchTodoTab('completed')"
                >{{ $t('common.completed') }}</div>
              </div>
            </div>
            <div class="list-content">
              <el-table 
                v-if="activeTodoTab === 'pending'" 
                :data="pendingTasks" 
                style="width: 100%" 
                :show-header="true" 
                height="300"
              >
                <el-table-column :label="$t('common.type')" width="80">
                  <template #default="{ row }">
                    <span class="event-type" :class="getEventTypeClass(row.type)">{{ row.type }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="title" :label="$t('common.title')" min-width="100" show-overflow-tooltip />
                <el-table-column prop="date" :label="$t('common.deadline')" width="90" />
                <el-table-column :label="$t('common.status')" width="80">
                  <template #default="{ row }">
                    <span :class="getStatusClass(row.status)">{{ row.status }}</span>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('common.action')" width="60" fixed="right">
                  <template #default>
                    <el-button type="primary" size="small" class="action-btn" @click="goToTodoPage">{{ $t('common.handle') }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <el-table 
                v-else 
                :data="completedTasks" 
                style="width: 100%" 
                :show-header="true" 
                height="300"
              >
                <el-table-column :label="$t('common.type')" width="80">
                  <template #default="{ row }">
                    <span class="event-type" :class="getEventTypeClass(row.type)">{{ row.type }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="title" :label="$t('common.title')" min-width="100" show-overflow-tooltip />
                <el-table-column prop="date" :label="$t('common.updateTime')" width="90" />
                <el-table-column :label="$t('common.status')" width="80">
                  <template #default="{ row }">
                    <span class="status-completed">{{ row.status }}</span>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('common.action')" width="60" fixed="right">
                  <template #default="{ row }">
                    <el-button type="info" size="small" class="action-btn" @click="viewTodoDetail(row.id)">{{ $t('common.detail') }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="24" :md="8">
          <div class="list-container">
            <div class="list-header">
              <div class="tab-group">
                <div
                  :class="['tab', {'active': activeInitiateTab === 'initiated'}]"
                  @click="switchInitiateTab('initiated')"
                >{{ $t('common.initiated') }}</div>
                <div
                  :class="['tab', {'active': activeInitiateTab === 'received'}]"
                  @click="switchInitiateTab('received')"
                >{{ $t('common.received') }}</div>
              </div>
            </div>
            <div class="list-content">
              <el-table 
                v-if="activeInitiateTab === 'initiated'"
                :data="initiatedTasks" 
                style="width: 100%" 
                :show-header="true" 
                height="300"
              >
                <el-table-column :label="$t('common.type')" min-width="70" align="center">
                  <template #default="{ row }">
                    <span class="event-type" :class="getEventTypeClass(row.type)">{{ row.type }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="sender" :label="$t('common.initiator')" min-width="60" align="center" />
                <el-table-column prop="date" :label="$t('common.initiateTime')" min-width="80" align="center" />
                <el-table-column label="状态" min-width="60" align="center">
                  <template #default="{ row }">
                    <span :class="getStatusClass(row.status)">{{ row.status }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="55" align="center" fixed="right">
                  <template #default>
                    <el-button type="primary" size="small" class="action-btn">详情</el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <el-table 
                v-else
                :data="receivedTasks" 
                style="width: 100%" 
                :show-header="true" 
                height="300"
              >
                <el-table-column label="类型" min-width="70" align="center">
                  <template #default="{ row }">
                    <span class="event-type" :class="getEventTypeClass(row.type)">{{ row.type }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="sender" label="发送人" min-width="60" align="center" />
                <el-table-column prop="date" label="接收时间" min-width="80" align="center" />
                <el-table-column label="状态" min-width="60" align="center">
                  <template #default="{ row }">
                    <span :class="getStatusClass(row.status)">{{ row.status }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="55" align="center" fixed="right">
                  <template #default>
                    <el-button type="primary" size="small" class="action-btn">回复</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="24" :md="8">
          <div class="chart-container">
            <div class="chart-header">
              <div class="tab-group">
                <div class="tab active">实时汇率走势</div>
              </div>
            </div>
            <div class="chart-body">
              <div class="scrollable-content" @scroll="handleScroll" ref="scrollContainer">
                <!-- 汇率卡片显示 -->
                <div class="exchange-rate-cards">
                  <div class="rate-card" v-for="(rate, key) in exchangeRateCards" :key="key">
                    <div class="rate-card-header">
                      <span class="currency-pair">{{ rate.pair }}</span>
                      <span class="rate-change" :class="getChangeClass(rate.change)">
                        {{ formatChange(rate.change) }}
                      </span>
                    </div>
                    <div class="rate-value">{{ rate.value || '--' }}</div>
                    <div class="rate-trend">
                      <div class="mini-chart" :ref="el => setMiniChartRef(key, el)"></div>
                    </div>
                  </div>
                </div>

                <!-- 主要汇率走势图 -->
                <div class="exchange-rate-chart">
                  <div ref="exchangeRateChartRef" style="width: 100%; height: 160px;"></div>
                </div>

                <div class="last-update" v-if="exchangeRates.lastUpdate">
                  最后更新: {{ formatTime(exchangeRates.lastUpdate) }}
                </div>
              </div>

              <!-- 滚动提示 -->
              <div class="scroll-indicator" v-show="showScrollIndicator">
                <i class="el-icon-arrow-down"></i>
                <span>向下滚动查看更多</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 日历和预警并排在同一行 -->
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="14">
          <div class="warning-container">
            <div class="list-header">
              <div class="tab-group">
                <div class="tab active">生产计划</div>
              </div>
            </div>
            <el-table :data="warningList" style="width: 100%" row-class-name="warning-row" height="310" :empty-text="'暂无生产计划'">
              <el-table-column prop="studentId" label="计划编号" min-width="120" align="center" />
              <el-table-column prop="name" label="产品名称" min-width="120" align="left" />
              <el-table-column prop="studentType" label="产品规格" min-width="120" align="left" show-overflow-tooltip />
              <el-table-column prop="protectionId" label="计划数量" min-width="100" align="center" />
              <el-table-column label="状态" min-width="80" align="center">
                <template #default="{ row }">
                  <span :class="getWarningTypeClass(row.status)">{{ row.warningType }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" min-width="80" align="center" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" size="small" class="warning-action-btn" @click="viewProductionPlan(row.id)" :disabled="row.id === 0">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="24" :md="10">
          <div class="calendar-container">
            <div class="calendar-header">
              <div class="month-selector">
                <el-icon class="month-arrow" @click="changeMonth(-1)"><ArrowLeft /></el-icon>
                <span>{{ currentMonthStr }}</span>
                <el-icon class="month-arrow" @click="changeMonth(1)"><ArrowRight /></el-icon>
              </div>
              <el-button link class="more-btn">更多</el-button>
            </div>
            <div class="calendar-alert">{{ currentYear }}年{{ currentMonth }}月{{ currentDay }}日</div>
            <div class="calendar-content">
              <div class="weekdays-header">
                <div class="weekday">日</div>
                <div class="weekday">一</div>
                <div class="weekday">二</div>
                <div class="weekday">三</div>
                <div class="weekday">四</div>
                <div class="weekday">五</div>
                <div class="weekday">六</div>
              </div>
              <div class="days-grid">
                <div class="day-cell" v-for="day in calendarDays" :key="day.date">
                  <div :class="['day-number', {'has-events': day.hasEvents, 'current': day.isCurrentDay, 'other-month': !day.isCurrentMonth}]">
                    {{ day.date }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onUpdated, onActivated, watch, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import * as echarts from 'echarts/core'
import {
  BarChart,
  LineChart
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import {
  UserFilled,
  Bell,
  Warning,
  Avatar,
  Location,
  School,
  ArrowLeft,
  ArrowRight,
  Document
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { todoApi, productionApi } from '../../services/api'
import axios from 'axios'
import { baseDataApi } from '../../services/api'

// 注册ECharts组件
echarts.use([
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  CanvasRenderer
])

const router = useRouter()
const authStore = useAuthStore()

// 上次加载时间
const lastLoadTime = ref(0)
const refreshInterval = 30000 // 30秒刷新间隔

// 用户信息
const userProfile = ref(null)
const isLoadingProfile = ref(true)

// 待办事项数据
const pendingTasks = ref([])
// 已办事项数据
const completedTasks = ref([])
// 所有待办事项
const todos = ref([])
// 当前激活的待办标签（待办/已办）
const activeTodoTab = ref('pending')

// 当前日期相关
const currentDate = ref(new Date())
const currentYear = computed(() => currentDate.value.getFullYear())
const currentMonth = computed(() => currentDate.value.getMonth() + 1) // getMonth() 返回 0-11
const currentDay = computed(() => currentDate.value.getDate())
const currentMonthStr = computed(() => `${currentYear.value}年${currentMonth.value}月`)

// 我发起/我收到的标签
const activeInitiateTab = ref('initiated')

// 外汇数据
const exchangeRates = ref({
  USDCNY: '--',
  EURCNY: '--',
  GBPCNY: '--',
  JPYCNY: '--',
  USDCNY_change: 0,
  EURCNY_change: 0,
  GBPCNY_change: 0,
  JPYCNY_change: 0,
  lastUpdate: null
})
const exchangeRateLoading = ref(false)

// 汇率历史数据
const exchangeRateHistory = ref({
  USDCNY: [],
  EURCNY: [],
  GBPCNY: [],
  JPYCNY: []
})

// 汇率卡片数据
const exchangeRateCards = computed(() => ({
  USDCNY: {
    pair: 'USD/CNY',
    value: exchangeRates.value.USDCNY,
    change: exchangeRates.value.USDCNY_change
  },
  EURCNY: {
    pair: 'EUR/CNY',
    value: exchangeRates.value.EURCNY,
    change: exchangeRates.value.EURCNY_change
  },
  GBPCNY: {
    pair: 'GBP/CNY',
    value: exchangeRates.value.GBPCNY,
    change: exchangeRates.value.GBPCNY_change
  },
  JPYCNY: {
    pair: 'JPY/CNY',
    value: exchangeRates.value.JPYCNY,
    change: exchangeRates.value.JPYCNY_change
  }
}))

// 图表引用
const exchangeRateChartRef = ref(null)
const miniChartRefs = ref({})
let exchangeRateChart = null
const miniCharts = ref({})

// 滚动相关
const scrollContainer = ref(null)
const showScrollIndicator = ref(true)

// 设置迷你图表引用
const setMiniChartRef = (key, el) => {
  if (el) {
    miniChartRefs.value[key] = el
  }
}

// 处理滚动事件
const handleScroll = (event) => {
  const container = event.target
  const scrollTop = container.scrollTop
  const scrollHeight = container.scrollHeight
  const clientHeight = container.clientHeight

  // 如果滚动到底部附近，隐藏滚动指示器
  if (scrollTop + clientHeight >= scrollHeight - 10) {
    showScrollIndicator.value = false
  } else if (scrollTop === 0) {
    showScrollIndicator.value = true
  }
}

// 加载用户数据
const loadUserProfile = async (force = false) => {
  try {
    const now = Date.now()
    // 如果距离上次加载不到30秒，且不是强制刷新，则跳过
    if (!force && (now - lastLoadTime.value < refreshInterval)) {
      return
    }
    
    isLoadingProfile.value = true
    await authStore.fetchUserProfile()
    userProfile.value = authStore.user
    lastLoadTime.value = now
  } catch (error) {
    console.error('获取用户信息失败:', error)
  } finally {
    isLoadingProfile.value = false
  }
}

// 部门信息现在直接从 userProfile.department_name 获取

// 图表引用（已移除，改为外汇显示）

// 我发起的事项数据
const initiatedTasks = ref([])

// 我收到的事项数据
const receivedTasks = ref([])

// 预警列表数据（生产计划）
const warningList = ref([])

// 获取事件类型样式
const getEventTypeClass = (type) => {
  const typeMap = {
    '英语变更': 'event-english',
    '新生指导': 'event-guide',
    '报到注册': 'event-register',
    '护照变更': 'event-passport',
    '活动报名': 'event-activity',
    '活动': 'event-activity'
  }
  return typeMap[type] || ''
}

// 获取状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    '待确认': 'status-pending',
    '未读': 'status-unread',
    '已读': 'status-read',
    '进行中': 'status-processing',
    '关闭': 'status-closed'
  }
  return statusMap[status] || ''
}

// 获取预警类型（生产计划状态）样式类
const getWarningTypeClass = (status) => {
  const typeMap = {
    'draft': 'warning-notice',
    'preparing': 'warning-notice',
    'material_issuing': 'warning-document',
    'material_issued': 'warning-document',
    'in_progress': 'warning-activity',
    'inspection': 'warning-course',
    'warehousing': 'warning-accommodation',
    'completed': 'warning-completed',
    'cancelled': 'warning-cancelled'
  }
  return typeMap[status] || 'warning-notice'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    draft: '未开始',
    preparing: '配料中',
    material_issuing: '发料中',
    material_issued: '已发料',
    in_progress: '生产中',
    inspection: '检验中',
    warehousing: '入库中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 获取外汇数据
const fetchExchangeRates = async () => {
  exchangeRateLoading.value = true
  try {
    // 使用免费的汇率API
    const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD')
    const data = await response.json()

    if (data && data.rates) {
      // 保存之前的汇率用于计算变化
      const prevRates = { ...exchangeRates.value }

      // 计算对人民币的汇率
      const usdToCny = data.rates.CNY || 0
      const eurToCny = data.rates.EUR ? (data.rates.CNY / data.rates.EUR) : 0
      const gbpToCny = data.rates.GBP ? (data.rates.CNY / data.rates.GBP) : 0
      const jpyToCny = data.rates.JPY ? (data.rates.CNY / data.rates.JPY) : 0

      // 计算变化
      const usdChange = prevRates.USDCNY !== '--' ? usdToCny - parseFloat(prevRates.USDCNY) : 0
      const eurChange = prevRates.EURCNY !== '--' ? eurToCny - parseFloat(prevRates.EURCNY) : 0
      const gbpChange = prevRates.GBPCNY !== '--' ? gbpToCny - parseFloat(prevRates.GBPCNY) : 0
      const jpyChange = prevRates.JPYCNY !== '--' ? jpyToCny - parseFloat(prevRates.JPYCNY) : 0

      const currentTime = new Date()

      // 更新历史数据（保留最近24个数据点）
      const updateHistory = (currency, value) => {
        const history = exchangeRateHistory.value[currency]
        history.push({
          time: currentTime.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
          value: parseFloat(value)
        })
        if (history.length > 24) {
          history.shift()
        }
      }

      updateHistory('USDCNY', usdToCny.toFixed(4))
      updateHistory('EURCNY', eurToCny.toFixed(4))
      updateHistory('GBPCNY', gbpToCny.toFixed(4))
      updateHistory('JPYCNY', jpyToCny.toFixed(6))

      exchangeRates.value = {
        USDCNY: usdToCny.toFixed(4),
        EURCNY: eurToCny.toFixed(4),
        GBPCNY: gbpToCny.toFixed(4),
        JPYCNY: jpyToCny.toFixed(6),
        USDCNY_change: usdChange,
        EURCNY_change: eurChange,
        GBPCNY_change: gbpChange,
        JPYCNY_change: jpyChange,
        lastUpdate: currentTime
      }

      // 更新图表
      updateExchangeRateChart()
      updateMiniCharts()
    }
  } catch (error) {
    console.error('获取汇率数据失败:', error)
    ElMessage.error('获取汇率数据失败，请检查网络连接')
  } finally {
    exchangeRateLoading.value = false
  }
}

// 刷新汇率
const refreshExchangeRate = async () => {
  await fetchExchangeRates()
  ElMessage.success('汇率数据已更新')
}

// 初始化汇率走势图
const initExchangeRateChart = () => {
  if (!exchangeRateChartRef.value) return

  exchangeRateChart = echarts.init(exchangeRateChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function(params) {
        let result = params[0].name + '<br/>'
        params.forEach(param => {
          result += `${param.seriesName}: ${param.value}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['USD/CNY', 'EUR/CNY', 'GBP/CNY'],
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
      axisLabel: {
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      scale: true,
      axisLabel: {
        fontSize: 10
      }
    },
    series: [
      {
        name: 'USD/CNY',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          color: '#409EFF',
          width: 2
        },
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        },
        data: []
      },
      {
        name: 'EUR/CNY',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          color: '#67C23A',
          width: 2
        },
        itemStyle: {
          color: '#67C23A'
        },
        data: []
      },
      {
        name: 'GBP/CNY',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          color: '#E6A23C',
          width: 2
        },
        itemStyle: {
          color: '#E6A23C'
        },
        data: []
      }
    ]
  }

  exchangeRateChart.setOption(option)
}

// 更新汇率走势图
const updateExchangeRateChart = () => {
  if (!exchangeRateChart) return

  const timeData = exchangeRateHistory.value.USDCNY.map(item => item.time)
  const usdData = exchangeRateHistory.value.USDCNY.map(item => item.value)
  const eurData = exchangeRateHistory.value.EURCNY.map(item => item.value)
  const gbpData = exchangeRateHistory.value.GBPCNY.map(item => item.value)

  exchangeRateChart.setOption({
    xAxis: {
      data: timeData
    },
    series: [
      { data: usdData },
      { data: eurData },
      { data: gbpData }
    ]
  })
}

// 初始化迷你图表
const initMiniChart = (key, container) => {
  if (!container) return

  const chart = echarts.init(container)
  const history = exchangeRateHistory.value[key] || []
  const data = history.map(item => item.value)
  const isPositive = exchangeRates.value[`${key}_change`] >= 0

  const option = {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    xAxis: {
      type: 'category',
      show: false,
      data: history.map(item => item.time)
    },
    yAxis: {
      type: 'value',
      show: false,
      scale: true
    },
    series: [{
      type: 'line',
      smooth: true,
      symbol: 'none',
      lineStyle: {
        color: isPositive ? '#67C23A' : '#F56C6C',
        width: 1.5
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: isPositive ? 'rgba(103, 194, 58, 0.3)' : 'rgba(245, 108, 108, 0.3)' },
            { offset: 1, color: isPositive ? 'rgba(103, 194, 58, 0.1)' : 'rgba(245, 108, 108, 0.1)' }
          ]
        }
      },
      data: data
    }]
  }

  chart.setOption(option)
  miniCharts.value[key] = chart
}

// 更新迷你图表
const updateMiniCharts = () => {
  Object.keys(miniChartRefs.value).forEach(key => {
    const container = miniChartRefs.value[key]
    if (container && !miniCharts.value[key]) {
      initMiniChart(key, container)
    } else if (miniCharts.value[key]) {
      const history = exchangeRateHistory.value[key] || []
      const data = history.map(item => item.value)
      const isPositive = exchangeRates.value[`${key}_change`] >= 0

      miniCharts.value[key].setOption({
        xAxis: {
          data: history.map(item => item.time)
        },
        series: [{
          data: data,
          lineStyle: {
            color: isPositive ? '#67C23A' : '#F56C6C'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: isPositive ? 'rgba(103, 194, 58, 0.3)' : 'rgba(245, 108, 108, 0.3)' },
                { offset: 1, color: isPositive ? 'rgba(103, 194, 58, 0.1)' : 'rgba(245, 108, 108, 0.1)' }
              ]
            }
          }
        }]
      })
    }
  })
}

// 格式化变化值
const formatChange = (change) => {
  if (!change || change === 0) return '0.0000'
  const sign = change > 0 ? '+' : ''
  return `${sign}${change.toFixed(4)}`
}

// 获取变化样式类
const getChangeClass = (change) => {
  if (!change || change === 0) return 'neutral'
  return change > 0 ? 'positive' : 'negative'
}

// 格式化时间
const formatTime = (date) => {
  return new Date(date).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 外汇数据定时刷新
let exchangeRateTimer = null
const startExchangeRateTimer = () => {
  // 每5分钟自动刷新一次汇率
  exchangeRateTimer = setInterval(() => {
    fetchExchangeRates()
  }, 5 * 60 * 1000)
}

const stopExchangeRateTimer = () => {
  if (exchangeRateTimer) {
    clearInterval(exchangeRateTimer)
    exchangeRateTimer = null
  }
}

// 加载用户待办事项
const loadUserTodos = async () => {
  try {
    // 检查登录状态
    if (!authStore.isAuthenticated) {
      console.warn('用户未登录，无法加载待办事项')
      return
    }
    
    const response = await todoApi.getAllTodos()
    console.log('待办事项API响应:', response)
    
    // 保存所有待办事项
    todos.value = response.data.data.map(todo => ({
      ...todo,
      deadline: new Date(todo.deadline)
    }))
    
    // 转换为待办事项显示格式
    pendingTasks.value = todos.value
      .filter(todo => !todo.completed) // 只显示未完成的
      .slice(0, 10) // 限制最多显示10个
      .map(todo => ({
        type: getPriorityType(todo.priority), // 根据优先级显示不同类型
        sender: authStore.user?.real_name || '用户',
        date: formatDate(todo.deadline),
        status: isOverdue(todo) ? '已逾期' : isUpcoming(todo) ? '即将到期' : '待处理',
        title: todo.title,
        id: todo.id
      }))
      
    // 转换为已办事项显示格式
    completedTasks.value = todos.value
      .filter(todo => todo.completed) // 只显示已完成的
      .slice(0, 10) // 限制最多显示10个
      .map(todo => ({
        type: getPriorityType(todo.priority),
        sender: authStore.user?.real_name || '用户',
        date: formatDate(todo.completed_at || todo.updated_at || todo.deadline),
        status: '已完成',
        title: todo.title,
        id: todo.id
      }))
      
    // 更新任务统计数量
    updateTaskStats()
    
    // 更新日历
    calendarDays.value = generateCalendarDays(currentDate.value)
  } catch (error) {
    console.error('加载待办事项失败:', error)
    // 如果是认证问题，可能需要重新登录
    if (error.response && error.response.status === 401) {
      ElMessage.error('登录已过期，请重新登录')
    }
  }
}

// 更新任务统计数量
const updateTaskStats = () => {
  // 计算待办事项总数
  const todoCount = todos.value.filter(todo => !todo.completed).length
  
  // 获取展示待办事项的card元素
  const todoCard = document.querySelector('.stat-card:nth-child(2) .number')
  if (todoCard) {
    todoCard.textContent = todoCount.toString()
  }
}

// 根据优先级返回不同的类型
const getPriorityType = (priority) => {
  switch (priority) {
    case 3: return '高优先级' // 高优先级
    case 2: return '中优先级' // 中优先级
    case 1: return '低优先级' // 低优先级
    default: return '待办事项'
  }
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '--'
  if (typeof date === 'string') date = new Date(date)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 判断是否过期
const isOverdue = (todo) => {
  if (!todo.deadline) return false
  const now = new Date()
  return new Date(todo.deadline) < now
}

// 判断是否即将到期（24小时内）
const isUpcoming = (todo) => {
  if (!todo.deadline) return false
  const now = new Date()
  const deadline = new Date(todo.deadline)
  const diff = deadline - now
  return diff > 0 && diff < 24 * 60 * 60 * 1000
}

// 加载生产计划数据
const loadProductionPlans = async () => {
  try {
    // 检查登录状态
    if (!authStore.isAuthenticated) {
      console.warn('用户未登录，无法加载生产计划')
      return
    }
    
    const params = {
      page: 1,
      limit: 10,
      sort: 'created_at',
      order: 'desc' // 最新的生产计划优先
    }
    
    // 获取生产计划列表
    const response = await productionApi.getProductionPlans(params)
    
    // 检查响应数据结构，适应多种可能的返回格式
    if (response.data) {
      let plans = []
      
      // 根据API返回的不同结构提取数据
      if (Array.isArray(response.data)) {
        plans = response.data
      } else if (response.data.items && Array.isArray(response.data.items)) {
        plans = response.data.items
      } else if (response.data.data && Array.isArray(response.data.data)) {
        plans = response.data.data
      }
      
      if (plans.length > 0) {
        // 获取所有产品ID，用于批量获取物料信息
        const productIds = plans
          .filter(plan => plan.product_id)
          .map(plan => plan.product_id);
        
        // 创建物料映射表
        const materialsMap = {};
        
        // 如果有产品ID，尝试批量获取物料信息
        if (productIds.length > 0) {
          try {
            // 使用baseDataApi获取物料信息
            const materialsResponse = await baseDataApi.getMaterials({
              page: 1,
              pageSize: 1000,
              ids: productIds.join(',')
            });
            
            // 处理不同的响应格式
            let materialsData = [];
            if (materialsResponse.data && materialsResponse.data.items) {
              materialsData = materialsResponse.data.items;
            } else if (materialsResponse.data && materialsResponse.data.data) {
              materialsData = materialsResponse.data.data;
            } else if (Array.isArray(materialsResponse.data)) {
              materialsData = materialsResponse.data;
            }
            
            // 将物料信息转换为映射表
            materialsData.forEach(material => {
              materialsMap[material.id] = material;
            });
          } catch (error) {
            console.error('批量获取物料信息失败:', error);
          }
        }
        
        // 处理每个计划的规格信息
        const processedPlans = await Promise.all(plans.map(async (plan) => {
          // 获取规格信息
          let specification = plan.specification || plan.specs || '';
          
          // 如果规格为空，尝试从物料表中获取
          if (!specification && plan.product_id && materialsMap[plan.product_id]) {
            specification = materialsMap[plan.product_id].specs || '';
          }
          
          // 如果仍然为空，尝试单独获取产品规格
          if (!specification && plan.product_id) {
            try {
              const productResponse = await baseDataApi.getMaterial(plan.product_id);
              
              if (productResponse.data && productResponse.data.specs) {
                specification = productResponse.data.specs;
              }
            } catch (error) {
              console.error(`获取产品ID=${plan.product_id}规格信息失败:`, error);
            }
          }
          
          return {
            ...plan,
            specification: specification // 更新规格信息
          };
        }));
        
        // 转换为仪表盘显示格式
        warningList.value = processedPlans.map(plan => {
          const specValue = plan.specification || plan.specs || plan.material_specs || plan.spec || plan.standard || '';
          
          return {
            studentId: plan.code || '无编号', // 使用计划编号
            name: plan.productName || plan.product_name || plan.name || '未命名', // 使用产品名称
            studentType: specValue || '无规格', // 使用产品规格，增加更多可能的字段
            protectionId: `${plan.quantity || 0}${plan.unit || '个'}`, // 显示计划数量
            warningType: getStatusText(plan.status || 'draft'), // 显示计划状态
            status: plan.status || 'draft', // 原始状态值
            id: plan.id, // 保存ID用于后续操作
            startDate: plan.start_date || plan.startDate,
            endDate: plan.end_date || plan.endDate
          };
        });
        
        // 更新预警数量统计
        updateWarningStats(plans.length)
      } else {
        // 如果没有数据
        console.warn('未找到生产计划数据')
        updateWarningStats(0)
      }
    } else {
      console.warn('获取生产计划失败或格式不正确:', response)
    }
  } catch (error) {
    console.error('加载生产计划失败:', error)
    if (error.response && error.response.status === 401) {
      ElMessage.error('登录已过期，请重新登录')
    } else {
      ElMessage.error('获取生产计划数据失败')
    }
  }
}

// 更新预警统计数量
const updateWarningStats = (count) => {
  // 获取展示预警的card元素
  const warningCard = document.querySelector('.stat-card:nth-child(3) .number')
  if (warningCard) {
    warningCard.textContent = count.toString()
  }
}

// 查看生产计划详情
const viewProductionPlan = (id) => {
  if (!id) return
  router.push(`/production/plan?id=${id}`)
}

// 组件挂载时加载数据
onMounted(async () => {
  loadUserProfile(true)
  loadUserTodos() // 加载待办事项
  loadProductionPlans() // 加载生产计划数据

  // 初始化汇率图表
  await nextTick()
  initExchangeRateChart()

  // 获取外汇数据
  await fetchExchangeRates()

  // 设置定时刷新
  const intervalId = setInterval(() => {
    loadUserProfile()
    loadUserTodos() // 同时刷新待办事项
    loadProductionPlans() // 同时刷新生产计划数据
  }, refreshInterval)

  // 汇率数据定时刷新（每5分钟）
  const exchangeRateTimer = setInterval(() => {
    fetchExchangeRates()
  }, 5 * 60 * 1000)

  // 初始化日历
  calendarDays.value = generateCalendarDays(currentDate.value)

  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(intervalId)
    clearInterval(exchangeRateTimer)
    if (exchangeRateChart) {
      exchangeRateChart.dispose()
    }
    Object.values(miniCharts.value).forEach(chart => {
      if (chart) chart.dispose()
    })
  })
})

// 当页面被激活（如从其他页面返回）时重新加载用户数据
onActivated(() => {
  loadUserProfile(true)
  loadUserTodos() // 同时刷新待办事项
})

// 监听用户数据变化
watch(() => authStore.user, (newValue) => {
  if (newValue) {
    userProfile.value = newValue
  }
}, { deep: true })

// 监听日期变化，更新日历
watch(() => currentDate.value, (newValue) => {
  calendarDays.value = generateCalendarDays(newValue)
})

// 组件更新后的处理（已移除图表相关）

// 添加跳转到个人资料页面的方法
const goToTodoPage = () => {
  router.push({
    path: '/profile',
    query: { tab: 'todos' }  // 添加查询参数，指定要打开的标签页
  })
}

// 切换待办/已办标签
const switchTodoTab = (tab) => {
  activeTodoTab.value = tab
}

// 切换我发起/我收到标签
const switchInitiateTab = (tab) => {
  activeInitiateTab.value = tab
}

// 查看待办详情
const viewTodoDetail = (id) => {
  router.push({
    path: '/profile',
    query: { tab: 'todos', id: id }
  })
}

// 日历相关逻辑
const calendarDays = ref([])

// 切换月份
const changeMonth = (delta) => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + delta)
  currentDate.value = newDate
  // 日历会通过watch自动更新
}

// 生成日历天数
const generateCalendarDays = (date) => {
  const year = date.getFullYear()
  const month = date.getMonth()
  const today = new Date()
  
  // 当月第一天是星期几 (0-6, 0表示星期日)
  const firstDay = new Date(year, month, 1).getDay()
  
  // 当月天数
  const daysInMonth = new Date(year, month + 1, 0).getDate()
  
  // 上个月的天数
  const daysInLastMonth = new Date(year, month, 0).getDate()
  
  // 生成日历数组
  const days = []
  
  // 填充上个月的日期
  for (let i = 0; i < firstDay; i++) {
    const day = daysInLastMonth - firstDay + i + 1
    days.push({
      date: day,
      isCurrentMonth: false,
      isCurrentDay: false,
      hasEvents: false
    })
  }
  
  // 填充当前月的日期
  for (let i = 1; i <= daysInMonth; i++) {
    const isToday = year === today.getFullYear() && 
                    month === today.getMonth() && 
                    i === today.getDate()
    days.push({
      date: i,
      isCurrentMonth: true,
      isCurrentDay: isToday,
      hasEvents: hasTodoOnDay(i)
    })
  }
  
  // 填充下个月的日期
  const remainingCells = 42 - days.length // 保证6行
  for (let i = 1; i <= remainingCells; i++) {
    days.push({
      date: i,
      isCurrentMonth: false,
      isCurrentDay: false,
      hasEvents: false
    })
  }
  
  return days
}

// 检查某天是否有待办事项
const hasTodoOnDay = (day) => {
  if (!todos.value || todos.value.length === 0) return false
  
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const targetDate = new Date(year, month, day)
  
  // 检查是否有当天的待办
  return todos.value.some(todo => {
    if (!todo.deadline) return false
    const deadline = new Date(todo.deadline)
    return deadline.getFullYear() === year &&
           deadline.getMonth() === month &&
           deadline.getDate() === day
  })
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-color: transparent;
  min-height: 100vh;
}

.main-layout {
  max-width: 1600px;
  margin: 0 auto;
}

/* 统计卡片样式 */
.stat-card {
  background-color: transparent;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 120px;
  display: flex;
  align-items: center;
}

.stat-content {
  display: flex;
  flex-direction: column;
  margin-left: 15px;
}

.purple {
  background-color: #9254DE;
}

/* 工作概览卡片样式 */
.overview-card {
  background-color: transparent;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.overview-items {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.overview-item {
  display: flex;
  align-items: center;
  flex: 1;
  padding: 0 15px;
}

.divider {
  width: 1px;
  height: 50px;
  background-color: #eee;
}

.icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.blue {
  background-color: #4B9EFF;
}

.red {
  background-color: #FF5050;
}

.green {
  background-color: #50DEFF;
}

.icon-container .el-icon {
  font-size: 20px;
  color: white;
}

.item-content {
  display: flex;
  flex-direction: column;
}

.number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.text {
  color: #666;
  font-size: 14px;
}

/* 个人信息样式 */
.personal-info-card {
  background-color: transparent;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  height: 120px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
}

.profile-section {
  display: flex;
  align-items: center;
  width: 100%;
  height: 80px; /* 固定内容区域高度 */
}

.profile-section.loading-state {
  justify-content: center;
}

.avatar {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 15px;
  flex-shrink: 0; /* 防止头像被压缩 */
}

.info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 70px; /* 确保与头像高度一致 */
}

.info .name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

.role-item {
  margin-bottom: 4px;
}

.icon-text {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.icon-text .el-icon {
  margin-right: 5px;
  font-size: 14px;
  color: #999;
}

/* 列表样式 */
.list-container {
  background-color: transparent;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 380px;
  display: flex;
  flex-direction: column;
}

.list-header {
  padding: 0;
  border-bottom: 1px solid #eee;
  flex-shrink: 0;
}

.tab-group {
  display: flex;
  height: 45px;
}

.tab {
  padding: 0 15px;
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  color: #666;
  font-size: 14px;
}

.tab.active {
  color: #4B9EFF;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #4B9EFF;
}

.list-content {
  padding: 0;
  flex: 1;
  overflow: auto;
}

/* 事件类型样式 */
.event-type {
  display: inline-block;
}

.event-english {
  color: #F56C6C;
}

.event-guide {
  color: #409EFF;
}

.event-register {
  color: #67C23A;
}

.event-passport {
  color: #E6A23C;
}

.event-activity {
  color: #F56C6C;
}

/* 状态样式 */
.status-pending {
  color: #E6A23C;
}

.status-unread {
  color: #F56C6C;
}

.status-read {
  color: #909399;
}

.status-processing {
  color: #67C23A;
}

.status-closed {
  color: #909399;
}

.status-completed {
  color: #67C23A;
}

.action-btn {
  padding: 3px 8px;
  font-size: 12px;
  height: 24px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: transparent;
  --el-table-header-bg-color: rgba(245, 247, 250, 0.5);
  --el-table-row-hover-bg-color: rgba(245, 247, 250, 0.5);
  background-color: transparent !important;
}

:deep(.el-table th) {
  background-color: rgba(245, 247, 250, 0.5);
  font-weight: normal;
  color: #606266;
  font-size: 13px;
  padding: 8px 0;
  height: 40px;
}

:deep(.el-table td) {
  padding: 8px 0;
  font-size: 13px;
  height: 40px;
  background-color: transparent !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: rgba(245, 247, 250, 0.5);
}

:deep(.el-table__inner-wrapper::before) {
  display: none;
}

/* 图表样式 */
.chart-container {
  background-color: transparent;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 380px;
  display: flex;
  flex-direction: column;
}

.chart-header {
  padding: 0;
  border-bottom: 1px solid #eee;
  flex-shrink: 0;
}

.chart-body {
  flex: 1;
  padding: 15px;
}

/* 日历样式 */
.calendar-container {
  background-color: transparent;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 15px;
  margin-bottom: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  height: 380px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.month-selector {
  display: flex;
  align-items: center;
  color: #333;
  font-weight: bold;
  font-size: 14px;
}

.month-arrow {
  margin: 0 8px;
  cursor: pointer;
  font-size: 14px;
  color: #909399;
}

.more-btn {
  color: #4B9EFF;
  font-size: 12px;
}

.calendar-alert {
  background-color: #f5f7fa;
  color: #333;
  padding: 8px;
  border-radius: 4px;
  text-align: center;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.calendar-content {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.weekdays-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  color: #606266;
  margin-bottom: 10px;
  font-size: 13px;
  flex-shrink: 0;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  flex: 1;
}

.day-cell {
  aspect-ratio: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 13px;
}

.day-number {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  cursor: pointer;
}

.day-number:hover {
  background-color: #e8f4ff;
}

.day-number.current {
  background-color: #409EFF;
  color: white;
  font-weight: bold;
}

.day-number.has-events {
  background-color: #FF6B6B;
  color: white;
}

.day-number.other-month {
  color: #c0c4cc;
  background-color: transparent;
}

/* 预警样式 */
.warning-container {
  background-color: transparent;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 0;
  margin-bottom: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 380px;
  display: flex;
  flex-direction: column;
}

.warning-document {
  color: #F56C6C;
}

.warning-course {
  color: #67C23A;
}

.warning-activity {
  color: #E6A23C;
}

.warning-accommodation {
  color: #409EFF;
}

.warning-action-btn {
  padding: 2px 10px;
  font-size: 12px;
  height: 24px;
  min-width: 50px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

/* 预警表格特定样式 */
.warning-container :deep(.el-table) {
  height: calc(100% - 46px); /* 减去标题栏的高度 */
}

.warning-container :deep(.el-table__body) {
  width: 100% !important;
}

.warning-container :deep(.el-table__header) {
  width: 100% !important;
}

@media (max-width: 768px) {
  .overview-items {
    flex-direction: column;
  }
  
  .overview-item {
    padding: 10px 0;
  }
  
  .divider {
    width: 100%;
    height: 1px;
    margin: 5px 0;
  }
  
  .personal-info-card {
    height: auto;
  }
}

/* 加载状态样式已整合到 .profile-section.loading-state */

/* 我发起表格特定样式 */
.list-container :deep(.el-table__body) {
  width: 100% !important;
}

.list-container :deep(.el-table__header) {
  width: 100% !important;
}

.list-container :deep(.el-table__body-wrapper) {
  overflow-x: hidden;
}

/* 滚动容器样式 */
.scrollable-content {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
}

/* 自定义滚动条样式 */
.scrollable-content::-webkit-scrollbar {
  width: 6px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 汇率卡片样式 */
.exchange-rate-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.rate-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 12px;
  color: white;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.rate-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.rate-card:nth-child(1) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.rate-card:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.rate-card:nth-child(3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.rate-card:nth-child(4) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.rate-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.currency-pair {
  font-size: 14px;
  font-weight: 600;
  opacity: 0.9;
}

.rate-value {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 6px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  flex: 1;
  display: flex;
  align-items: center;
}

.rate-trend {
  height: 35px;
  margin-top: 4px;
}

.mini-chart {
  width: 100%;
  height: 100%;
}

.exchange-rate-chart {
  background: white;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.rate-pair {
  font-size: 14px;
  font-weight: bold;
  color: #606266;
  margin-bottom: 5px;
}

.rate-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.rate-change {
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.rate-change.positive {
  color: #ffffff;
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

.rate-change.negative {
  color: #ffffff;
  background: rgba(244, 67, 54, 0.3);
  border-color: rgba(244, 67, 54, 0.5);
}

.rate-change.neutral {
  color: #ffffff;
  background: rgba(158, 158, 158, 0.3);
  border-color: rgba(158, 158, 158, 0.5);
}

.last-update {
  text-align: center;
  font-size: 12px;
  color: #909399;
  margin-top: 10px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #409EFF;
}

/* 滚动指示器样式 */
.scroll-indicator {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(64, 158, 255, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  animation: bounce 2s infinite;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.scroll-indicator i {
  font-size: 14px;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-5px);
  }
  60% {
    transform: translateX(-50%) translateY(-3px);
  }
}

/* 确保chart-body有相对定位 */
.chart-body {
  position: relative;
}
</style>